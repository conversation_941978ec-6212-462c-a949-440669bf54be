use crate::parquet::RecordBatchWrapper;
use arrow::datatypes::{DataType, Field, FieldRef};
use arrow::record_batch::RecordBatch;
use chrono::{DateTime, Utc};
use clickhouse::Row;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use crate::utils::decimal::Decimal38_18;

#[derive(Row, Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
#[allow(non_snake_case)]
pub struct SubTestItemDetail {
    pub FILE_ID: Option<i64>,
    pub ONLINE_RETEST: Option<i32>,
    pub MAX_OFFLINE_RETEST: Option<i32>,
    pub MAX_ONLINE_RETEST: Option<i32>,
    pub IS_DIE_FIRST_TEST: Option<i32>,
    pub IS_DIE_FINAL_TEST: Option<i32>,
    pub IS_FIRST_TEST: Option<i32>,
    pub IS_FINAL_TEST: Option<i32>,
    pub IS_FIRST_TEST_IGNORE_TP: Option<i32>,
    pub IS_FINAL_TEST_IGNORE_TP: Option<i32>,
    pub IS_DUP_FIRST_TEST: Option<i32>,
    pub IS_DUP_FINAL_TEST: Option<i32>,
    pub IS_DUP_FIRST_TEST_IGNORE_TP: Option<i32>,
    pub IS_DUP_FINAL_TEST_IGNORE_TP: Option<i32>,
    pub TEST_SUITE: Option<String>,
    pub CONDITION_SET: Option<HashMap<String, String>>,
    pub TEST_NUM: Option<i64>,
    pub TEST_TXT: Option<String>,
    pub TEST_ITEM: Option<String>,
    pub IS_DIE_FIRST_TEST_ITEM: Option<i32>,
    pub TESTITEM_TYPE: Option<String>,
    pub TEST_FLG: Option<String>,
    pub PARM_FLG: Option<String>,
    pub TEST_STATE: Option<String>,
    pub TEST_VALUE: Option<Decimal38_18>,
    pub UNITS: Option<String>,
    pub TEST_RESULT: Option<i32>,
    pub ORIGIN_TEST_VALUE: Option<Decimal38_18>,
    pub ORIGIN_UNITS: Option<String>,
    pub TEST_ORDER: Option<i64>,
    pub ALARM_ID: Option<String>,
    pub OPT_FLG: Option<String>,
    pub RES_SCAL: Option<i32>,
    pub NUM_TEST: Option<i32>,
    pub LLM_SCAL: Option<i32>,
    pub HLM_SCAL: Option<i32>,
    pub LO_LIMIT: Option<Decimal38_18>,
    pub HI_LIMIT: Option<Decimal38_18>,
    pub ORIGIN_HI_LIMIT: Option<Decimal38_18>,
    pub ORIGIN_LO_LIMIT: Option<Decimal38_18>,
    pub C_RESFMT: Option<String>,
    pub C_LLMFMT: Option<String>,
    pub C_HLMFMT: Option<String>,
    pub LO_SPEC: Option<Decimal38_18>,
    pub HI_SPEC: Option<Decimal38_18>,
    pub HBIN_NUM: Option<i64>,
    pub SBIN_NUM: Option<i64>,
    pub SBIN_PF: Option<String>,
    pub SBIN_NAM: Option<String>,
    pub HBIN_PF: Option<String>,
    pub HBIN_NAM: Option<String>,
    pub HBIN: Option<String>,
    pub SBIN: Option<String>,
    pub TEST_HEAD: Option<i64>,
    pub PART_FLG: Option<String>,
    pub PART_ID: Option<String>,
    pub C_PART_ID: Option<i64>,
    pub ECID: Option<String>,
    pub ECID_EXT: Option<String>,
    pub ECID_EXTRA: Option<HashMap<String, String>>,
    pub IS_STANDARD_ECID: Option<i32>,
    pub X_COORD: Option<i32>,
    pub Y_COORD: Option<i32>,
    pub DIE_X: Option<i32>,
    pub DIE_Y: Option<i32>,
    pub TEST_TIME: Option<i64>,
    pub PART_TXT: Option<String>,
    pub PART_FIX: Option<String>,
    pub SITE: Option<i64>,
    pub TOUCH_DOWN_ID: Option<i32>,
    pub WAFER_LOT_ID: Option<String>,
    pub WAFER_ID: Option<String>,
    pub WAFER_NO: Option<String>,
    pub RETICLE_T_X: Option<i32>,
    pub RETICLE_T_Y: Option<i32>,
    pub RETICLE_X: Option<i32>,
    pub RETICLE_Y: Option<i32>,
    pub SITE_ID: Option<String>,
    pub VECT_NAM: Option<String>,
    pub TIME_SET: Option<String>,
    pub NUM_FAIL: Option<i64>,
    pub FAIL_PIN: Option<String>,
    pub CYCL_CNT: Option<i64>,
    pub REPT_CNT: Option<i64>,
    pub LONG_ATTRIBUTE_SET: Option<HashMap<String, i64>>,
    pub STRING_ATTRIBUTE_SET: Option<HashMap<String, String>>,
    pub FLOAT_ATTRIBUTE_SET: Option<HashMap<String, Decimal38_18>>,
    pub UID: Option<String>,
    pub TEXT_DAT: Option<String>,
    pub CREATE_HOUR_KEY: Option<String>,
    pub CREATE_DAY_KEY: Option<String>,
    pub CREATE_TIME: DateTime<Utc>,
    pub EFUSE_EXTRA: Option<HashMap<String, String>>,
    pub CHIP_ID: Option<String>,
}

impl SubTestItemDetail {
    pub fn new() -> Self {
        Self {
            FILE_ID: None,
            ONLINE_RETEST: None,
            MAX_OFFLINE_RETEST: None,
            MAX_ONLINE_RETEST: None,
            IS_DIE_FIRST_TEST: None,
            IS_DIE_FINAL_TEST: None,
            IS_FIRST_TEST: None,
            IS_FINAL_TEST: None,
            IS_FIRST_TEST_IGNORE_TP: None,
            IS_FINAL_TEST_IGNORE_TP: None,
            IS_DUP_FIRST_TEST: None,
            IS_DUP_FINAL_TEST: None,
            IS_DUP_FIRST_TEST_IGNORE_TP: None,
            IS_DUP_FINAL_TEST_IGNORE_TP: None,
            TEST_SUITE: None,
            CONDITION_SET: None,
            TEST_NUM: None,
            TEST_TXT: None,
            TEST_ITEM: None,
            IS_DIE_FIRST_TEST_ITEM: None,
            TESTITEM_TYPE: None,
            TEST_FLG: None,
            PARM_FLG: None,
            TEST_STATE: None,
            TEST_VALUE: None,
            UNITS: None,
            TEST_RESULT: None,
            ORIGIN_TEST_VALUE: None,
            ORIGIN_UNITS: None,
            TEST_ORDER: None,
            ALARM_ID: None,
            OPT_FLG: None,
            RES_SCAL: None,
            NUM_TEST: None,
            LLM_SCAL: None,
            HLM_SCAL: None,
            LO_LIMIT: None,
            HI_LIMIT: None,
            ORIGIN_HI_LIMIT: None,
            ORIGIN_LO_LIMIT: None,
            C_RESFMT: None,
            C_LLMFMT: None,
            C_HLMFMT: None,
            LO_SPEC: None,
            HI_SPEC: None,
            HBIN_NUM: None,
            SBIN_NUM: None,
            SBIN_PF: None,
            SBIN_NAM: None,
            HBIN_PF: None,
            HBIN_NAM: None,
            HBIN: None,
            SBIN: None,
            TEST_HEAD: None,
            PART_FLG: None,
            PART_ID: None,
            C_PART_ID: None,
            ECID: None,
            ECID_EXT: None,
            ECID_EXTRA: None,
            IS_STANDARD_ECID: None,
            X_COORD: None,
            Y_COORD: None,
            DIE_X: None,
            DIE_Y: None,
            TEST_TIME: None,
            PART_TXT: None,
            PART_FIX: None,
            SITE: None,
            TOUCH_DOWN_ID: None,
            WAFER_LOT_ID: None,
            WAFER_ID: None,
            WAFER_NO: None,
            RETICLE_T_X: None,
            RETICLE_T_Y: None,
            RETICLE_X: None,
            RETICLE_Y: None,
            SITE_ID: None,
            VECT_NAM: None,
            TIME_SET: None,
            NUM_FAIL: None,
            FAIL_PIN: None,
            CYCL_CNT: None,
            REPT_CNT: None,
            LONG_ATTRIBUTE_SET: None,
            STRING_ATTRIBUTE_SET: None,
            FLOAT_ATTRIBUTE_SET: None,
            UID: None,
            TEXT_DAT: None,
            CREATE_HOUR_KEY: None,
            CREATE_DAY_KEY: None,
            CREATE_TIME: Utc::now(),
            EFUSE_EXTRA: None,
            CHIP_ID: None,
        }
    }
}

impl RecordBatchWrapper for SubTestItemDetail {
    fn from_record_batch(batch: &RecordBatch) -> Result<Vec<Self>, String>
    where
        Self: Sized,
    {
        let result: Vec<SubTestItemDetail> = serde_arrow::from_record_batch(batch)
            .map_err(|e| format!("Failed to deserialize from RecordBatch: {}", e))?;
        Ok(result)
    }

    fn to_record_batch(data: &Vec<Self>) -> Result<RecordBatch, String>
    where
        Self: Sized,
    {
        if data.is_empty() {
            return Err("Cannot create RecordBatch from empty data".to_string());
        }
        // map类型没有办法使用 from_type 来生成 schema, 而 from_samples 效率太低了并且可能造成schema不一致, 所以这里手工设置 schema
        let fields: Vec<FieldRef> = vec![
            Arc::new(Field::new("FILE_ID", DataType::Int64, true)),
            Arc::new(Field::new("ONLINE_RETEST", DataType::Int32, true)),
            Arc::new(Field::new("MAX_OFFLINE_RETEST", DataType::Int32, true)),
            Arc::new(Field::new("MAX_ONLINE_RETEST", DataType::Int32, true)),
            Arc::new(Field::new("IS_DIE_FIRST_TEST", DataType::Int32, true)),
            Arc::new(Field::new("IS_DIE_FINAL_TEST", DataType::Int32, true)),
            Arc::new(Field::new("IS_FIRST_TEST", DataType::Int32, true)),
            Arc::new(Field::new("IS_FINAL_TEST", DataType::Int32, true)),
            Arc::new(Field::new("IS_FIRST_TEST_IGNORE_TP", DataType::Int32, true)),
            Arc::new(Field::new("IS_FINAL_TEST_IGNORE_TP", DataType::Int32, true)),
            Arc::new(Field::new("IS_DUP_FIRST_TEST", DataType::Int32, true)),
            Arc::new(Field::new("IS_DUP_FINAL_TEST", DataType::Int32, true)),
            Arc::new(Field::new("IS_DUP_FIRST_TEST_IGNORE_TP", DataType::Int32, true)),
            Arc::new(Field::new("IS_DUP_FINAL_TEST_IGNORE_TP", DataType::Int32, true)),
            Arc::new(Field::new("TEST_SUITE", DataType::LargeUtf8, true)),
            Arc::new(Field::new("CONDITION_SET", DataType::Map(
                Arc::new(Field::new("entries", DataType::Struct(vec![
                    Arc::new(Field::new("key", DataType::LargeUtf8, false)),
                    Arc::new(Field::new("value", DataType::LargeUtf8, false)),
                ].into()), false)),
                false
            ), true)),
            Arc::new(Field::new("TEST_NUM", DataType::Int64, true)),
            Arc::new(Field::new("TEST_TXT", DataType::LargeUtf8, true)),
            Arc::new(Field::new("TEST_ITEM", DataType::LargeUtf8, true)),
            Arc::new(Field::new("IS_DIE_FIRST_TEST_ITEM", DataType::Int32, true)),
            Arc::new(Field::new("TESTITEM_TYPE", DataType::LargeUtf8, true)),
            Arc::new(Field::new("TEST_FLG", DataType::LargeUtf8, true)),
            Arc::new(Field::new("PARM_FLG", DataType::LargeUtf8, true)),
            Arc::new(Field::new("TEST_STATE", DataType::LargeUtf8, true)),
            Arc::new(Field::new("TEST_VALUE", DataType::Float64, true)),
            Arc::new(Field::new("UNITS", DataType::LargeUtf8, true)),
            Arc::new(Field::new("TEST_RESULT", DataType::Int32, true)),
            Arc::new(Field::new("ORIGIN_TEST_VALUE", DataType::Float64, true)),
            Arc::new(Field::new("ORIGIN_UNITS", DataType::LargeUtf8, true)),
            Arc::new(Field::new("TEST_ORDER", DataType::Int64, true)),
            Arc::new(Field::new("ALARM_ID", DataType::LargeUtf8, true)),
            Arc::new(Field::new("OPT_FLG", DataType::LargeUtf8, true)),
            Arc::new(Field::new("RES_SCAL", DataType::Int32, true)),
            Arc::new(Field::new("NUM_TEST", DataType::Int32, true)),
            Arc::new(Field::new("LLM_SCAL", DataType::Int32, true)),
            Arc::new(Field::new("HLM_SCAL", DataType::Int32, true)),
            Arc::new(Field::new("LO_LIMIT", DataType::Float64, true)),
            Arc::new(Field::new("HI_LIMIT", DataType::Float64, true)),
            Arc::new(Field::new("ORIGIN_HI_LIMIT", DataType::Float64, true)),
            Arc::new(Field::new("ORIGIN_LO_LIMIT", DataType::Float64, true)),
            Arc::new(Field::new("C_RESFMT", DataType::LargeUtf8, true)),
            Arc::new(Field::new("C_LLMFMT", DataType::LargeUtf8, true)),
            Arc::new(Field::new("C_HLMFMT", DataType::LargeUtf8, true)),
            Arc::new(Field::new("LO_SPEC", DataType::Float64, true)),
            Arc::new(Field::new("HI_SPEC", DataType::Float64, true)),
            Arc::new(Field::new("HBIN_NUM", DataType::Int64, true)),
            Arc::new(Field::new("SBIN_NUM", DataType::Int64, true)),
            Arc::new(Field::new("SBIN_PF", DataType::LargeUtf8, true)),
            Arc::new(Field::new("SBIN_NAM", DataType::LargeUtf8, true)),
            Arc::new(Field::new("HBIN_PF", DataType::LargeUtf8, true)),
            Arc::new(Field::new("HBIN_NAM", DataType::LargeUtf8, true)),
            Arc::new(Field::new("HBIN", DataType::LargeUtf8, true)),
            Arc::new(Field::new("SBIN", DataType::LargeUtf8, true)),
            Arc::new(Field::new("TEST_HEAD", DataType::Int64, true)),
            Arc::new(Field::new("PART_FLG", DataType::LargeUtf8, true)),
            Arc::new(Field::new("PART_ID", DataType::LargeUtf8, true)),
            Arc::new(Field::new("C_PART_ID", DataType::Int64, true)),
            Arc::new(Field::new("ECID", DataType::LargeUtf8, true)),
            Arc::new(Field::new("ECID_EXT", DataType::LargeUtf8, true)),
            Arc::new(Field::new("ECID_EXTRA", DataType::Map(
                Arc::new(Field::new("entries", DataType::Struct(vec![
                    Arc::new(Field::new("key", DataType::LargeUtf8, false)),
                    Arc::new(Field::new("value", DataType::LargeUtf8, false)),
                ].into()), false)),
                false
            ), true)),
            Arc::new(Field::new("IS_STANDARD_ECID", DataType::Int32, true)),
            Arc::new(Field::new("X_COORD", DataType::Int32, true)),
            Arc::new(Field::new("Y_COORD", DataType::Int32, true)),
            Arc::new(Field::new("DIE_X", DataType::Int32, true)),
            Arc::new(Field::new("DIE_Y", DataType::Int32, true)),
            Arc::new(Field::new("TEST_TIME", DataType::Int64, true)),
            Arc::new(Field::new("PART_TXT", DataType::LargeUtf8, true)),
            Arc::new(Field::new("PART_FIX", DataType::LargeUtf8, true)),
            Arc::new(Field::new("SITE", DataType::Int64, true)),
            Arc::new(Field::new("TOUCH_DOWN_ID", DataType::Int32, true)),
            Arc::new(Field::new("WAFER_LOT_ID", DataType::LargeUtf8, true)),
            Arc::new(Field::new("WAFER_ID", DataType::LargeUtf8, true)),
            Arc::new(Field::new("WAFER_NO", DataType::LargeUtf8, true)),
            Arc::new(Field::new("RETICLE_T_X", DataType::Int32, true)),
            Arc::new(Field::new("RETICLE_T_Y", DataType::Int32, true)),
            Arc::new(Field::new("RETICLE_X", DataType::Int32, true)),
            Arc::new(Field::new("RETICLE_Y", DataType::Int32, true)),
            Arc::new(Field::new("SITE_ID", DataType::LargeUtf8, true)),
            Arc::new(Field::new("VECT_NAM", DataType::LargeUtf8, true)),
            Arc::new(Field::new("TIME_SET", DataType::LargeUtf8, true)),
            Arc::new(Field::new("NUM_FAIL", DataType::Int64, true)),
            Arc::new(Field::new("FAIL_PIN", DataType::LargeUtf8, true)),
            Arc::new(Field::new("CYCL_CNT", DataType::Int64, true)),
            Arc::new(Field::new("REPT_CNT", DataType::Int64, true)),
            Arc::new(Field::new("LONG_ATTRIBUTE_SET", DataType::Map(
                Arc::new(Field::new("entries", DataType::Struct(vec![
                    Arc::new(Field::new("key", DataType::LargeUtf8, false)),
                    Arc::new(Field::new("value", DataType::Int64, false)),
                ].into()), false)),
                false
            ), true)),
            Arc::new(Field::new("STRING_ATTRIBUTE_SET", DataType::Map(
                Arc::new(Field::new("entries", DataType::Struct(vec![
                    Arc::new(Field::new("key", DataType::LargeUtf8, false)),
                    Arc::new(Field::new("value", DataType::LargeUtf8, false)),
                ].into()), false)),
                false
            ), true)),
            Arc::new(Field::new("FLOAT_ATTRIBUTE_SET", DataType::Map(
                Arc::new(Field::new("entries", DataType::Struct(vec![
                    Arc::new(Field::new("key", DataType::LargeUtf8, false)),
                    Arc::new(Field::new("value", DataType::Float64, false)),
                ].into()), false)),
                false
            ), true)),
            Arc::new(Field::new("UID", DataType::LargeUtf8, true)),
            Arc::new(Field::new("TEXT_DAT", DataType::LargeUtf8, true)),
            Arc::new(Field::new("CREATE_HOUR_KEY", DataType::LargeUtf8, true)),
            Arc::new(Field::new("CREATE_DAY_KEY", DataType::LargeUtf8, true)),
            Arc::new(Field::new("CREATE_TIME", DataType::LargeUtf8, false)),
            Arc::new(Field::new("EFUSE_EXTRA", DataType::Map(
                Arc::new(Field::new("entries", DataType::Struct(vec![
                    Arc::new(Field::new("key", DataType::LargeUtf8, false)),
                    Arc::new(Field::new("value", DataType::LargeUtf8, false)),
                ].into()), false)),
                false
            ), true)),
            Arc::new(Field::new("CHIP_ID", DataType::LargeUtf8, true)),
        ];
        let record_batch =
            serde_arrow::to_record_batch(&fields, data).map_err(|e| format!("Failed to create RecordBatch: {}", e))?;
        Ok(record_batch)
    }
}
