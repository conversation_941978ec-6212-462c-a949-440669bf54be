use crate::constant::file_type::PARQUET;
use crate::RecordBatchWrapper;
use log::info;
use parquet::arrow::arrow_reader::ParquetRecordBatchReaderBuilder;
use parquet::basic::{Compression, ZstdLevel};
use parquet::file::properties::WriterProperties;
use std::fs::File;
use std::fs::{self, DirEntry};
use std::path::{Path, PathBuf};

pub fn write_parquet<T>(dir_path: &str, data: &Vec<T>)
where
    T: RecordBatchWrapper,
{
    if data.is_empty() {
        log::warn!("Cannot write parquet file with empty data: {}", dir_path);
        return;
    }

    let record_batch = T::to_record_batch(data).unwrap();
    log::info!("record_batch num_rows: {:?}", record_batch.num_rows());

    if record_batch.num_rows() == 0 {
        log::warn!("Cannot write parquet file with empty record batch: {}", dir_path);
        return;
    }

    let file = File::create(dir_path).unwrap();

    let props = WriterProperties::builder()
        .set_compression(Compression::ZSTD(ZstdLevel::default()))
        .set_dictionary_enabled(true)
        .set_max_row_group_size(1 * 1024 * 1024)
        .build();

    let mut writer = parquet::arrow::ArrowWriter::try_new(file, record_batch.schema(), Some(props)).unwrap();
    writer.write(&record_batch).unwrap();
    writer.close().unwrap();
}

pub fn read_parquet<T>(dir_path: &str) -> Vec<T>
where
    T: RecordBatchWrapper,
{
    let parquet_files = find_parquet_files(dir_path).unwrap();

    // 读取并合并所有RecordBatch
    let mut all_batch = Vec::new();
    for file_path in parquet_files {
        let file = File::open(file_path).unwrap();
        let reader_builder = ParquetRecordBatchReaderBuilder::try_new(file).unwrap();
        let mut reader = reader_builder.build().unwrap();
        while let Some(batch) = reader.next() {
            all_batch.push(batch.unwrap());
        }
    }

    let result = all_batch
        .iter()
        .map(|data| T::from_record_batch(data).unwrap())
        .flatten()
        .collect::<Vec<T>>();
    info!("读取出: {}条记录", result.len());
    result
}

fn find_parquet_files(dir: &str) -> Result<Vec<PathBuf>, Box<dyn std::error::Error>> {
    let mut result = Vec::new();
    visit_dirs(Path::new(dir), &mut |entry| {
        if let Some(ext) = entry.path().extension() {
            if ext == PARQUET {
                result.push(entry.path().to_path_buf());
            }
        }
    })?;
    Ok(result)
}

fn visit_dirs(dir: &Path, cb: &mut dyn FnMut(&DirEntry)) -> std::io::Result<()> {
    if dir.is_dir() {
        for entry in fs::read_dir(dir)? {
            let entry = entry?;
            let path = entry.path();
            if path.is_dir() {
                visit_dirs(&path, cb)?;
            } else {
                cb(&entry);
            }
        }
    }
    Ok(())
}
